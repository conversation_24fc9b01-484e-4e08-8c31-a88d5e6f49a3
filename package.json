{"name": "microsoft-rewards-script", "version": "1.5.3", "description": "Automatically do tasks for Microsoft Rewards but in TS!", "main": "index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"pre-build": "npm i && rimraf dist && npx playwright install chromium", "build": "tsc", "start": "node ./dist/index.js", "ts-start": "ts-node ./src/index.ts", "dev": "ts-node ./src/index.ts -dev", "kill-chrome-win": "powershell -Command \"Get-Process | Where-Object { $_.MainModule.FileVersionInfo.FileDescription -eq 'Google Chrome for Testing' } | ForEach-Object { Stop-Process -Id $_.Id -Force }\"", "create-docker": "docker build -t microsoft-rewards-script-docker ."}, "keywords": ["<PERSON>", "Microsoft Rewards", "Bot", "<PERSON><PERSON><PERSON>", "TypeScript", "Playwright", "Ch<PERSON><PERSON>"], "author": "<PERSON><PERSON>", "license": "ISC", "devDependencies": {"@types/ms": "^0.7.34", "@typescript-eslint/eslint-plugin": "^7.17.0", "eslint": "^8.57.0", "eslint-plugin-modules-newline": "^0.0.6", "rimraf": "^6.0.1", "typescript": "^5.5.4"}, "dependencies": {"axios": "^1.8.4", "chalk": "^4.1.2", "cheerio": "^1.0.0", "fingerprint-generator": "^2.1.66", "fingerprint-injector": "^2.1.66", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "ms": "^2.1.3", "playwright": "1.47.2", "rebrowser-playwright": "1.47.2", "socks-proxy-agent": "^8.0.5", "ts-node": "^10.9.2"}}